/**
 * Utility functions for extracting colors from images to create gradient banners
 */

export interface ColorPalette {
  primary: string;
  secondary: string;
  accent: string;
  gradient: string;
}

/**
 * Extract dominant colors from an image URL
 */
export const extractColorsFromImage = async (imageUrl: string): Promise<ColorPalette> => {
  return new Promise((resolve) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';
    
    img.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      if (!ctx) {
        resolve(getDefaultPalette());
        return;
      }
      
      // Set canvas size
      canvas.width = img.width;
      canvas.height = img.height;
      
      // Draw image to canvas
      ctx.drawImage(img, 0, 0);
      
      try {
        // Get image data
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;
        
        // Extract colors using simple sampling
        const colors = extractDominantColors(data);
        
        // Create palette
        const palette = createPalette(colors);
        resolve(palette);
      } catch (error) {
        console.error('Error extracting colors:', error);
        resolve(getDefaultPalette());
      }
    };
    
    img.onerror = () => {
      resolve(getDefaultPalette());
    };
    
    img.src = imageUrl;
  });
};

/**
 * Extract dominant colors from image data
 */
const extractDominantColors = (data: Uint8ClampedArray): string[] => {
  const colorMap = new Map<string, number>();
  const step = 4; // Sample every 4th pixel for performance
  
  for (let i = 0; i < data.length; i += step * 4) {
    const r = data[i];
    const g = data[i + 1];
    const b = data[i + 2];
    const a = data[i + 3];
    
    // Skip transparent pixels
    if (a < 128) continue;
    
    // Skip very light or very dark colors
    const brightness = (r + g + b) / 3;
    if (brightness < 30 || brightness > 225) continue;
    
    // Reduce color precision for grouping
    const reducedR = Math.floor(r / 32) * 32;
    const reducedG = Math.floor(g / 32) * 32;
    const reducedB = Math.floor(b / 32) * 32;
    
    const colorKey = `${reducedR},${reducedG},${reducedB}`;
    colorMap.set(colorKey, (colorMap.get(colorKey) || 0) + 1);
  }
  
  // Sort by frequency and get top colors
  const sortedColors = Array.from(colorMap.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, 5)
    .map(([color]) => {
      const [r, g, b] = color.split(',').map(Number);
      return `rgb(${r}, ${g}, ${b})`;
    });
  
  return sortedColors.length > 0 ? sortedColors : ['rgb(255, 107, 0)', 'rgb(255, 140, 0)'];
};

/**
 * Create a color palette from extracted colors
 */
const createPalette = (colors: string[]): ColorPalette => {
  const primary = colors[0] || 'rgb(255, 107, 0)';
  const secondary = colors[1] || 'rgb(255, 140, 0)';
  const accent = colors[2] || 'rgb(255, 165, 0)';
  
  // Convert to hex for easier manipulation
  const primaryHex = rgbToHex(primary);
  const secondaryHex = rgbToHex(secondary);
  
  // Create gradient
  const gradient = `linear-gradient(135deg, ${primary} 0%, ${secondary} 50%, ${darkenColor(primaryHex, 20)} 100%)`;
  
  return {
    primary: primaryHex,
    secondary: secondaryHex,
    accent: rgbToHex(accent),
    gradient,
  };
};

/**
 * Convert RGB string to hex
 */
const rgbToHex = (rgb: string): string => {
  const match = rgb.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
  if (!match) return '#FF6B00';
  
  const r = parseInt(match[1]);
  const g = parseInt(match[2]);
  const b = parseInt(match[3]);
  
  return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;
};

/**
 * Darken a hex color by a percentage
 */
const darkenColor = (hex: string, percent: number): string => {
  const num = parseInt(hex.replace('#', ''), 16);
  const amt = Math.round(2.55 * percent);
  const R = (num >> 16) - amt;
  const G = (num >> 8 & 0x00FF) - amt;
  const B = (num & 0x0000FF) - amt;
  
  return `#${(0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
    (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
    (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1)}`;
};

/**
 * Get default color palette
 */
const getDefaultPalette = (): ColorPalette => ({
  primary: '#FF6B00',
  secondary: '#FF8C00',
  accent: '#FFA500',
  gradient: 'linear-gradient(135deg, #FF6B00 0%, #FF8C00 50%, #E55A00 100%)',
});

/**
 * Generate a gradient background style from image URL
 */
export const generateGradientFromImage = async (imageUrl: string): Promise<string> => {
  try {
    const palette = await extractColorsFromImage(imageUrl);
    return palette.gradient;
  } catch (error) {
    console.error('Error generating gradient:', error);
    return getDefaultPalette().gradient;
  }
};

/**
 * Get complementary colors for text based on background
 */
export const getTextColors = (backgroundColor: string): { primary: string; secondary: string } => {
  // Simple brightness check - in a real app you'd want more sophisticated color analysis
  const isLight = backgroundColor.includes('255') || backgroundColor.includes('white');
  
  return {
    primary: isLight ? '#1a1a1a' : '#ffffff',
    secondary: isLight ? '#666666' : '#cccccc',
  };
};
