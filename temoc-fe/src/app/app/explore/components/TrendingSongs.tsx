import { Button } from '@/components/common';
import Card from '@/components/ui/Card/Card';
import { FaLongArrowAltRight } from 'react-icons/fa';
import React from 'react';
import CommonSlider from '@/components/common/CommonSlider';
import { useQuery } from '@tanstack/react-query';
import { contentApi } from '@/services/content';
import Loader from '@/components/common/Loader';
import { useMusicPlayer } from '@/context/MusicPlayerContext';

interface Iprops {
  setexploreDetail?: any;
}

const TrendingSongs = ({ }: Iprops) => {
  const { playTrack, setShowTrackPage } = useMusicPlayer();

  const { data: trendingSongsResponse, isLoading } = useQuery<any>({
    queryKey: ['discovery', 'recommended'],
    queryFn: () => contentApi.getTrendingSongs(),
  });

  // Extract the tracks array from the response
  const trendingSongs = trendingSongsResponse?.data || trendingSongsResponse || [];

  if (isLoading) {
    return (
      <div className="mt-8">
        <h3 className="text-base font-semibold uppercase sm:text-xl">
          Trending Songs
        </h3>
        <div className="flex h-52 items-center justify-center">
          <Loader />
        </div>
      </div>
    );
  }
  if (!trendingSongs || trendingSongs?.length === 0) {
    return (
      <div className="mt-8">
        <h3 className="text-base font-semibold uppercase sm:text-xl">
          Trending Songs
        </h3>
        <p className="mt-2 flex h-52 items-center justify-center text-center font-bold text-gray-500">
          No trending songs available at the moment.
        </p>
      </div>
    );
  }

  return (
    <div className="">
      <div className="mt-8 flex items-center justify-between">
        <h3 className="text-base font-semibold uppercase sm:text-xl">
          Trending Songs
        </h3>
        <Button className="flex items-center gap-2 !border-none !bg-transparent !text-[#FF6E00]">
          <u>View All</u> <FaLongArrowAltRight className="text-black" />
        </Button>
      </div>
      <div className="mt-2">
        <CommonSlider delay={true}>
          {trendingSongs?.map((item: any, index: number) => (
            <div
              onClick={() => {
                // Play the track and show track page instead of Billie Eilish page
                playTrack(item, trendingSongs);
                setShowTrackPage(true);
              }}
              key={index}
              className="mb-3 cursor-pointer"
            >
              <Card
                imageSrc={item.thumbnailUrl || '/assets/images/women.avif'}
                name={item.title} // Show track title instead of album title
                profileSrc={
                  item.creator.avatarUrl || '/assets/images/women.avif'
                }
                artistName={item.artist || item.creator.displayName}
                playIcon={false} // Enable hover play icon
              />
            </div>
          ))}
        </CommonSlider>
      </div>
    </div>
  );
};

export default TrendingSongs;
