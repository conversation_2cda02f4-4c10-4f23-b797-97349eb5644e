@import 'tailwindcss/base';
@import './components/Typo.css';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@layer base {
  p {
    @apply text-sm sm:text-base;
  }
  h2 {
    @apply font-DreamAvenue !text-[38px] !font-normal text-[#292D32] md:!text-[80px];
  }
}

h1,
h2,
h3,
h4,
h5,
h6 {
  line-height: 1 !important;
}
.heading {
  font-size: 20px;
}
.bd {
  border: 1px solid red;
}
.AtSocialSlider1 .swiper-pagination-bullet {
  border: 1px solid #ff8000 !important;
  height: 8px !important;
  background-color: transparent !important;
  width: 8px !important;
  transition: all 0.3s;
  opacity: 1;
}
.AtSocialSlider1 .swiper-pagination-bullet-active {
  background-color: #ff8000 !important;
  opacity: 1;
  width: 30px !important; /* Expanded width */
  height: 8px !important;
  border-radius: 24px !important;
  margin-right: -5px;
}
.AtSocialSlider1 .swiper-pagination {
  position: relative !important;
  margin-top: 50px !important;
}

.hideScrollbar::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
}

.hideScrollbar {
  scrollbar-width: none !important;
}

.dynamic-shadow-dom {
  --dynamic-connect-button-background: #ff6e00;
  --dynamic-connect-button-color: white;
  --dynamic-connect-button-border: 1px solid #ff6e00;
  --dynamic-connect-button-shadow: none;
  --dynamic-connect-button-background-hover: #ff6e00;
  --dynamic-connect-button-color-hover: white;
  --dynamic-connect-button-border-hover: 1px solid #ff6e00;
  --dynamic-connect-button-shadow-hover: none;
  --dynamic-tooltip-color: #000;
  --dynamic-tooltip-text-color: #fff;
  --dynamic-connect-button-radius: 5rem;
}

.AtSocialSliderSignup .swiper-pagination-bullet {
  border: 2px solid white !important;
  height: 10px !important;
  background-color: transparent !important;
  width: 10px !important;
  transition: all 0.3s;
  opacity: 1;
}
.AtSocialSliderSignup .swiper-pagination-bullet-active {
  background-color: white !important;
  opacity: 1;
  width: 20px !important; /* Expanded width */
  height: 10px !important;
  border-radius: 24px !important;
  margin-right: -5px;
}
.AtSocialSliderSignup .swiper-pagination {
  /* position: relative !important; */
  /* margin-top: 50px !important; */
}
.Atbg {
  background-size: 100% 100%;
}
input[type='number']::-webkit-outer-spin-button,
input[type='number']::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0; /* Optional: Adjust margins */
}
.shadow {
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);
}
.no-scroll {
  overflow: hidden;
}
.bgBlur {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/*  */

.chakra-avatar__excess {
  z-index: 100 !important;
  height: 40px;
  width: 40px;
  background-color: gainsboro;
  border-radius: 50%;
}
