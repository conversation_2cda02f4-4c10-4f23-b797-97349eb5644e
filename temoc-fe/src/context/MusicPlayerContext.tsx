'use client';
import React, { createContext, useContext, useState, useRef, useEffect, ReactNode } from 'react';
import { contentApi } from '@/services/content';

export interface Track {
  _id: string;
  title: string;
  artist: string;
  fileUrl: string;
  thumbnailUrl: string;
  duration: number;
  creator: {
    _id: string;
    displayName: string;
    avatarUrl: string;
    username: string;
  };
  album: {
    _id: string;
    title: string;
    coverPicture: string;
  };
  playCount: number;
}

interface MusicPlayerContextType {
  // Current track state
  currentTrack: Track | null;
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  
  // Queue management
  queue: Track[];
  currentIndex: number;
  isShuffled: boolean;
  isLooping: boolean;
  
  // Player controls
  playTrack: (track: Track, trackList?: Track[]) => void;
  pauseTrack: () => void;
  resumeTrack: () => void;
  nextTrack: () => void;
  previousTrack: () => void;
  seekTo: (time: number) => void;
  setVolume: (volume: number) => void;
  toggleShuffle: () => void;
  toggleLoop: () => void;
  
  // Queue controls
  addToQueue: (track: Track) => void;
  removeFromQueue: (index: number) => void;
  clearQueue: () => void;
  
  // UI state
  showPlayer: boolean;
  showQueue: boolean;
  setShowQueue: (show: boolean) => void;
  showTrackPage: boolean;
  setShowTrackPage: (show: boolean) => void;
}

const MusicPlayerContext = createContext<MusicPlayerContextType | undefined>(undefined);

export const useMusicPlayer = () => {
  const context = useContext(MusicPlayerContext);
  if (!context) {
    throw new Error('useMusicPlayer must be used within a MusicPlayerProvider');
  }
  return context;
};

export const MusicPlayerProvider = ({ children }: { children: ReactNode }) => {
  // Audio element ref
  const audioRef = useRef<HTMLAudioElement>(null);
  
  // Player state
  const [currentTrack, setCurrentTrack] = useState<Track | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolumeState] = useState(1);
  
  // Queue state
  const [queue, setQueue] = useState<Track[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isShuffled, setIsShuffled] = useState(false);
  const [isLooping, setIsLooping] = useState(false);
  
  // UI state
  const [showPlayer, setShowPlayer] = useState(false);
  const [showQueue, setShowQueue] = useState(false);
  const [showTrackPage, setShowTrackPage] = useState(false);

  // Audio event handlers
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleTimeUpdate = () => setCurrentTime(audio.currentTime);
    const handleDurationChange = () => setDuration(audio.duration || 0);
    const handleEnded = () => {
      if (isLooping) {
        audio.currentTime = 0;
        audio.play();
      } else {
        nextTrack();
      }
    };
    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);

    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('durationchange', handleDurationChange);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('play', handlePlay);
    audio.addEventListener('pause', handlePause);

    return () => {
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('durationchange', handleDurationChange);
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('play', handlePlay);
      audio.removeEventListener('pause', handlePause);
    };
  }, [isLooping]);

  // Player controls
  const playTrack = async (track: Track, trackList?: Track[]) => {
    const audio = audioRef.current;
    if (!audio) return;

    // Set up queue if trackList provided
    if (trackList) {
      setQueue(trackList);
      const trackIndex = trackList.findIndex(t => t._id === track._id);
      setCurrentIndex(trackIndex >= 0 ? trackIndex : 0);
    } else if (queue.length === 0) {
      setQueue([track]);
      setCurrentIndex(0);
    }

    setCurrentTrack(track);
    audio.src = track.fileUrl;
    
    try {
      await audio.play();
      setShowPlayer(true);
      
      // Record play count
      await contentApi.recordTrackPlay(track._id);
    } catch (error) {
      console.error('Error playing track:', error);
    }
  };

  const pauseTrack = () => {
    const audio = audioRef.current;
    if (audio) {
      audio.pause();
    }
  };

  const resumeTrack = () => {
    const audio = audioRef.current;
    if (audio) {
      audio.play();
    }
  };

  const nextTrack = () => {
    if (queue.length === 0) return;
    
    let nextIndex = currentIndex + 1;
    if (nextIndex >= queue.length) {
      nextIndex = 0; // Loop back to start
    }
    
    setCurrentIndex(nextIndex);
    playTrack(queue[nextIndex]);
  };

  const previousTrack = () => {
    if (queue.length === 0) return;
    
    let prevIndex = currentIndex - 1;
    if (prevIndex < 0) {
      prevIndex = queue.length - 1; // Loop to end
    }
    
    setCurrentIndex(prevIndex);
    playTrack(queue[prevIndex]);
  };

  const seekTo = (time: number) => {
    const audio = audioRef.current;
    if (audio) {
      audio.currentTime = time;
    }
  };

  const setVolume = (newVolume: number) => {
    const audio = audioRef.current;
    if (audio) {
      audio.volume = newVolume;
      setVolumeState(newVolume);
    }
  };

  const toggleShuffle = () => {
    setIsShuffled(!isShuffled);
    // TODO: Implement shuffle logic
  };

  const toggleLoop = () => {
    setIsLooping(!isLooping);
  };

  // Queue controls
  const addToQueue = (track: Track) => {
    setQueue(prev => [...prev, track]);
  };

  const removeFromQueue = (index: number) => {
    setQueue(prev => prev.filter((_, i) => i !== index));
    if (index < currentIndex) {
      setCurrentIndex(prev => prev - 1);
    } else if (index === currentIndex && queue.length > 1) {
      // If removing current track, play next one
      nextTrack();
    }
  };

  const clearQueue = () => {
    setQueue([]);
    setCurrentIndex(0);
    pauseTrack();
    setCurrentTrack(null);
    setShowPlayer(false);
  };

  const value: MusicPlayerContextType = {
    currentTrack,
    isPlaying,
    currentTime,
    duration,
    volume,
    queue,
    currentIndex,
    isShuffled,
    isLooping,
    playTrack,
    pauseTrack,
    resumeTrack,
    nextTrack,
    previousTrack,
    seekTo,
    setVolume,
    toggleShuffle,
    toggleLoop,
    addToQueue,
    removeFromQueue,
    clearQueue,
    showPlayer,
    showQueue,
    setShowQueue,
    showTrackPage,
    setShowTrackPage,
  };

  return (
    <MusicPlayerContext.Provider value={value}>
      {children}
      {/* Hidden audio element */}
      <audio ref={audioRef} preload="metadata" />
    </MusicPlayerContext.Provider>
  );
};
