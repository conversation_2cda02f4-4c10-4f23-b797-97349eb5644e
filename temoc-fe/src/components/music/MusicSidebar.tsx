'use client';
import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { discoveryService } from '@/services/discovery';
import { contentApi } from '@/services/content';
import { useMusicPlayer } from '@/context/MusicPlayerContext';
import ImageComponent from '@/components/common/ImageComponent';
import { FaPlay, FaUserPlus } from 'react-icons/fa';
import Skeleton from 'react-loading-skeleton';

const MusicSidebar = () => {
  const { playTrack } = useMusicPlayer();

  // Fetch popular artists
  const { data: popularArtists, isLoading: artistsLoading } = useQuery({
    queryKey: ['popular-artists'],
    queryFn: () => discoveryService.getPopularArtists(5),
  });

  // Fetch trending tracks for playlists
  const { data: trendingTracksResponse, isLoading: tracksLoading } = useQuery({
    queryKey: ['trending-tracks-sidebar'],
    queryFn: () => contentApi.getTrendingSongs(),
  });

  // Extract the tracks array from the response
  const trendingTracks = trendingTracksResponse?.data || trendingTracksResponse || [];

  return (
    <div className="w-80 bg-gray-50 p-6">
      {/* Popular Artists Section */}
      <div className="mb-8">
        <h3 className="mb-4 text-lg font-semibold text-gray-900">Popular Artists</h3>
        <div className="space-y-3">
          {artistsLoading ? (
            Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-center space-x-3">
                <Skeleton circle height={48} width={48} />
                <div className="flex-1">
                  <Skeleton height={16} width="60%" />
                  <Skeleton height={12} width="40%" className="mt-1" />
                </div>
                <Skeleton height={32} width={64} />
              </div>
            ))
          ) : (
            popularArtists?.slice(0, 5).map((artist: any) => (
              <div key={artist._id} className="flex items-center space-x-3 rounded-lg p-2 transition-colors hover:bg-gray-100">
                <div className="h-12 w-12 overflow-hidden rounded-full">
                  <ImageComponent
                    src={artist.artistProfile?.profilePic || artist.avatarUrl || '/assets/images/women.avif'}
                    alt={artist.displayName}
                    className="h-full w-full object-cover"
                    fill
                  />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="truncate text-sm font-medium text-gray-900">
                    {artist.displayName}
                  </p>
                  <p className="truncate text-xs text-gray-500">
                    {artist.followersCount} followers
                  </p>
                </div>
                <button className="flex items-center space-x-1 rounded-full bg-primary px-3 py-1 text-xs text-white transition-colors hover:bg-orange-600">
                  <FaUserPlus className="text-xs" />
                  <span>Follow</span>
                </button>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Trending Playlists Section */}
      <div className="mb-8">
        <h3 className="mb-4 text-lg font-semibold text-gray-900">Trending Now</h3>
        <div className="space-y-3">
          {tracksLoading ? (
            Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="flex items-center space-x-3">
                <Skeleton height={40} width={40} />
                <div className="flex-1">
                  <Skeleton height={14} width="70%" />
                  <Skeleton height={12} width="50%" className="mt-1" />
                </div>
                <Skeleton circle height={24} width={24} />
              </div>
            ))
          ) : (
            trendingTracks?.slice(0, 6).map((track: any, index: number) => (
              <div 
                key={track._id} 
                className="group flex items-center space-x-3 rounded-lg p-2 transition-colors hover:bg-gray-100"
              >
                <div className="relative h-10 w-10 overflow-hidden rounded">
                  <ImageComponent
                    src={track.thumbnailUrl || '/assets/images/women.avif'}
                    alt={track.title}
                    className="h-full w-full object-cover"
                    fill
                  />
                  <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 opacity-0 transition-opacity group-hover:opacity-100">
                    <button
                      onClick={() => playTrack(track, trendingTracks)}
                      className="flex h-6 w-6 items-center justify-center rounded-full bg-white text-black"
                    >
                      <FaPlay className="ml-0.5 text-xs" />
                    </button>
                  </div>
                </div>
                <div className="min-w-0 flex-1">
                  <p className="truncate text-sm font-medium text-gray-900">
                    {track.title}
                  </p>
                  <p className="truncate text-xs text-gray-500">
                    {track.artist || track.creator.displayName}
                  </p>
                </div>
                <div className="text-xs text-gray-400">
                  #{index + 1}
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Recently Played Section */}
      <div>
        <h3 className="mb-4 text-lg font-semibold text-gray-900">Recently Played</h3>
        <div className="space-y-3">
          <div className="flex items-center justify-center rounded-lg bg-gray-100 p-8 text-center">
            <div>
              <div className="mb-2 text-2xl text-gray-400">🎵</div>
              <p className="text-sm text-gray-500">Start listening to see your recent tracks</p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mt-8 space-y-2">
        <button className="w-full rounded-lg bg-primary px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-orange-600">
          Create Playlist
        </button>
        <button className="w-full rounded-lg border border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-50">
          Browse All
        </button>
      </div>
    </div>
  );
};

export default MusicSidebar;
