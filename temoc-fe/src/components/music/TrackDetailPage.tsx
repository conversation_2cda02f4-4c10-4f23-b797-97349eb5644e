'use client';
import React, { useState, useEffect } from 'react';
import { useMusicPlayer } from '@/context/MusicPlayerContext';
import { FaPlay, FaPause, FaHeart, FaShare, FaComment, FaArrowLeft } from 'react-icons/fa';
import { IoAdd } from 'react-icons/io5';
import ImageComponent from '@/components/common/ImageComponent';
import { generateGradientFromImage, getTextColors } from '@/utils/colorExtractor';

const TrackDetailPage = () => {
  const {
    currentTrack,
    isPlaying,
    currentTime,
    duration,
    showTrackPage,
    setShowTrackPage,
    playTrack,
    pauseTrack,
    resumeTrack,
    addToQueue,
  } = useMusicPlayer();

  const [gradientBackground, setGradientBackground] = useState('');
  const [textColors, setTextColors] = useState({ primary: '#ffffff', secondary: '#cccccc' });
  const [isLiked, setIsLiked] = useState(false);
  const [showComments, setShowComments] = useState(false);

  // Generate gradient from track thumbnail
  useEffect(() => {
    if (currentTrack?.thumbnailUrl) {
      generateGradientFromImage(currentTrack.thumbnailUrl).then((gradient) => {
        setGradientBackground(gradient);
        setTextColors(getTextColors(gradient));
      });
    }
  }, [currentTrack]);

  if (!showTrackPage || !currentTrack) return null;

  const formatTime = (time: number): string => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handlePlayPause = () => {
    if (isPlaying) {
      pauseTrack();
    } else {
      resumeTrack();
    }
  };

  const progressPercentage = duration ? (currentTime / duration) * 100 : 0;

  return (
    <div className="fixed inset-0 z-50 bg-black">
      {/* Header with Gradient Background */}
      <div 
        className="relative h-80 overflow-hidden"
        style={{ background: gradientBackground || 'linear-gradient(135deg, #FF6B00 0%, #FF8C00 50%, #E55A00 100%)' }}
      >
        {/* Back Button */}
        <button
          onClick={() => setShowTrackPage(false)}
          className="absolute left-4 top-4 z-10 flex h-10 w-10 items-center justify-center rounded-full bg-black bg-opacity-30 text-white transition-colors hover:bg-opacity-50"
        >
          <FaArrowLeft className="text-sm" />
        </button>

        {/* Track Info */}
        <div className="absolute bottom-0 left-0 right-0 p-6">
          <div className="flex items-end space-x-6">
            {/* Track Artwork */}
            <div className="h-48 w-48 flex-shrink-0 overflow-hidden rounded-lg shadow-2xl">
              <ImageComponent
                src={currentTrack.thumbnailUrl || '/assets/images/women.avif'}
                alt={currentTrack.title}
                className="h-full w-full object-cover"
                fill
              />
            </div>

            {/* Track Details */}
            <div className="min-w-0 flex-1 pb-4">
              <p className="text-sm font-medium" style={{ color: textColors.secondary }}>
                TRACK
              </p>
              <h1 
                className="mt-2 text-4xl font-bold md:text-6xl"
                style={{ color: textColors.primary }}
              >
                {currentTrack.title}
              </h1>
              <div className="mt-4 flex items-center space-x-2">
                <div className="h-6 w-6 overflow-hidden rounded-full">
                  <ImageComponent
                    src={currentTrack.creator.avatarUrl || '/assets/images/women.avif'}
                    alt={currentTrack.creator.displayName}
                    className="h-full w-full object-cover"
                    fill
                  />
                </div>
                <span 
                  className="text-sm font-medium"
                  style={{ color: textColors.primary }}
                >
                  {currentTrack.artist || currentTrack.creator.displayName}
                </span>
                <span style={{ color: textColors.secondary }}>•</span>
                <span 
                  className="text-sm"
                  style={{ color: textColors.secondary }}
                >
                  {currentTrack.playCount} plays
                </span>
                <span style={{ color: textColors.secondary }}>•</span>
                <span 
                  className="text-sm"
                  style={{ color: textColors.secondary }}
                >
                  {formatTime(duration)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Controls Section */}
      <div className="bg-gradient-to-b from-black/20 to-black p-6">
        <div className="flex items-center space-x-6">
          {/* Play Button */}
          <button
            onClick={handlePlayPause}
            className="flex h-14 w-14 items-center justify-center rounded-full bg-primary text-white transition-transform hover:scale-105"
          >
            {isPlaying ? (
              <FaPause className="text-lg" />
            ) : (
              <FaPlay className="ml-1 text-lg" />
            )}
          </button>

          {/* Action Buttons */}
          <button
            onClick={() => setIsLiked(!isLiked)}
            className={`p-3 transition-colors ${
              isLiked ? 'text-red-500' : 'text-gray-400 hover:text-white'
            }`}
          >
            <FaHeart className="text-lg" />
          </button>

          <button
            onClick={() => addToQueue(currentTrack)}
            className="p-3 text-gray-400 transition-colors hover:text-white"
          >
            <IoAdd className="text-lg" />
          </button>

          <button className="p-3 text-gray-400 transition-colors hover:text-white">
            <FaShare className="text-lg" />
          </button>

          <button
            onClick={() => setShowComments(!showComments)}
            className="p-3 text-gray-400 transition-colors hover:text-white"
          >
            <FaComment className="text-lg" />
          </button>
        </div>

        {/* Waveform/Progress Bar */}
        <div className="mt-6">
          <div className="relative h-16 w-full overflow-hidden rounded bg-gray-800">
            {/* Simplified waveform visualization */}
            <div className="flex h-full items-end justify-center space-x-1 p-2">
              {Array.from({ length: 100 }).map((_, i) => (
                <div
                  key={i}
                  className={`w-1 rounded-t transition-colors ${
                    i < progressPercentage ? 'bg-primary' : 'bg-gray-600'
                  }`}
                  style={{
                    height: `${Math.random() * 80 + 20}%`,
                  }}
                />
              ))}
            </div>
            
            {/* Progress indicator */}
            <div
              className="absolute top-0 h-full w-1 bg-white"
              style={{ left: `${progressPercentage}%` }}
            />
          </div>
          
          {/* Time indicators */}
          <div className="mt-2 flex justify-between text-sm text-gray-400">
            <span>{formatTime(currentTime)}</span>
            <span>{formatTime(duration)}</span>
          </div>
        </div>
      </div>

      {/* Content Area */}
      <div className="flex-1 overflow-y-auto bg-black p-6">
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Artist Info */}
            <div className="mb-8">
              <h3 className="mb-4 text-lg font-semibold text-white">Artist</h3>
              <div className="flex items-center space-x-4">
                <div className="h-16 w-16 overflow-hidden rounded-full">
                  <ImageComponent
                    src={currentTrack.creator.avatarUrl || '/assets/images/women.avif'}
                    alt={currentTrack.creator.displayName}
                    className="h-full w-full object-cover"
                    fill
                  />
                </div>
                <div>
                  <p className="font-medium text-white">
                    {currentTrack.creator.displayName}
                  </p>
                  <p className="text-sm text-gray-400">
                    @{currentTrack.creator.username}
                  </p>
                </div>
                <button className="ml-auto rounded-full border border-gray-600 px-4 py-2 text-sm text-white transition-colors hover:bg-white hover:text-black">
                  Follow
                </button>
              </div>
            </div>

            {/* Comments Section */}
            {showComments && (
              <div>
                <h3 className="mb-4 text-lg font-semibold text-white">Comments</h3>
                <div className="space-y-4">
                  <div className="rounded-lg bg-gray-900 p-4">
                    <p className="text-sm text-gray-300">
                      Comments feature coming soon...
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div>
            <h3 className="mb-4 text-lg font-semibold text-white">Related Tracks</h3>
            <div className="space-y-3">
              {/* Placeholder for related tracks */}
              <div className="text-sm text-gray-400">
                Related tracks will appear here...
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TrackDetailPage;
