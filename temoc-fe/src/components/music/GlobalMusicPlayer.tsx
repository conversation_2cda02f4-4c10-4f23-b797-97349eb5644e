'use client';
import React, { useState, useEffect } from 'react';
import { useMusicPlayer } from '@/context/MusicPlayerContext';
import { FaPlay, FaPause, FaStepForward, FaStepBackward, FaVolumeUp, FaList, FaTimes } from 'react-icons/fa';
import { IoShuffle, IoRepeat } from 'react-icons/io5';
import ImageComponent from '@/components/common/ImageComponent';

const GlobalMusicPlayer = () => {
  const {
    currentTrack,
    isPlaying,
    currentTime,
    duration,
    volume,
    playTrack,
    pauseTrack,
    resumeTrack,
    nextTrack,
    previousTrack,
    seekTo,
    setVolume,
    toggleShuffle,
    toggleLoop,
    isShuffled,
    isLooping,
    showPlayer,
    showQueue,
    setShowQueue,
    clearQueue,
    setShowTrackPage,
  } = useMusicPlayer();

  const [showVolumeSlider, setShowVolumeSlider] = useState(false);

  if (!showPlayer || !currentTrack) return null;

  const formatTime = (time: number): string => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const newTime = (clickX / rect.width) * duration;
    seekTo(newTime);
  };

  const handlePlayPause = () => {
    if (isPlaying) {
      pauseTrack();
    } else {
      resumeTrack();
    }
  };

  const handleTrackClick = () => {
    setShowTrackPage(true);
  };

  return (
    <>
      {/* Main Player Bar */}
      <div className="fixed bottom-0 left-0 right-0 z-50 border-t border-gray-200 bg-white shadow-lg">
        <div className="mx-auto max-w-full px-4 py-3">
          <div className="flex items-center justify-between">
            {/* Track Info */}
            <div className="flex min-w-0 flex-1 items-center space-x-3">
              <div 
                className="h-12 w-12 flex-shrink-0 cursor-pointer overflow-hidden rounded-lg bg-gray-200 transition-transform hover:scale-105"
                onClick={handleTrackClick}
              >
                <ImageComponent
                  src={currentTrack.thumbnailUrl || '/assets/images/women.avif'}
                  alt={currentTrack.title}
                  className="h-full w-full object-cover"
                  fill
                />
              </div>
              <div className="min-w-0 flex-1">
                <p 
                  className="cursor-pointer truncate text-sm font-medium text-gray-900 hover:text-primary"
                  onClick={handleTrackClick}
                >
                  {currentTrack.title}
                </p>
                <p className="truncate text-xs text-gray-500">
                  {currentTrack.artist || currentTrack.creator.displayName}
                </p>
              </div>
            </div>

            {/* Player Controls */}
            <div className="flex flex-1 flex-col items-center space-y-2">
              {/* Control Buttons */}
              <div className="flex items-center space-x-4">
                <button
                  onClick={toggleShuffle}
                  className={`p-1 transition-colors ${
                    isShuffled ? 'text-primary' : 'text-gray-400 hover:text-gray-600'
                  }`}
                >
                  <IoShuffle className="text-sm" />
                </button>
                
                <button
                  onClick={previousTrack}
                  className="p-1 text-gray-600 transition-colors hover:text-gray-900"
                >
                  <FaStepBackward className="text-sm" />
                </button>
                
                <button
                  onClick={handlePlayPause}
                  className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-white transition-transform hover:scale-105"
                >
                  {isPlaying ? (
                    <FaPause className="text-sm" />
                  ) : (
                    <FaPlay className="ml-0.5 text-sm" />
                  )}
                </button>
                
                <button
                  onClick={nextTrack}
                  className="p-1 text-gray-600 transition-colors hover:text-gray-900"
                >
                  <FaStepForward className="text-sm" />
                </button>
                
                <button
                  onClick={toggleLoop}
                  className={`p-1 transition-colors ${
                    isLooping ? 'text-primary' : 'text-gray-400 hover:text-gray-600'
                  }`}
                >
                  <IoRepeat className="text-sm" />
                </button>
              </div>

              {/* Progress Bar */}
              <div className="flex w-full max-w-md items-center space-x-2">
                <span className="text-xs text-gray-500">{formatTime(currentTime)}</span>
                <div
                  className="relative h-1 flex-1 cursor-pointer rounded-full bg-gray-300"
                  onClick={handleProgressClick}
                >
                  <div
                    className="absolute h-full rounded-full bg-primary transition-all"
                    style={{ width: `${duration ? (currentTime / duration) * 100 : 0}%` }}
                  />
                  <div
                    className="absolute top-1/2 h-3 w-3 -translate-y-1/2 rounded-full bg-primary opacity-0 transition-opacity hover:opacity-100"
                    style={{ left: `${duration ? (currentTime / duration) * 100 : 0}%` }}
                  />
                </div>
                <span className="text-xs text-gray-500">{formatTime(duration)}</span>
              </div>
            </div>

            {/* Right Controls */}
            <div className="flex min-w-0 flex-1 items-center justify-end space-x-3">
              {/* Volume Control */}
              <div className="relative">
                <button
                  onClick={() => setShowVolumeSlider(!showVolumeSlider)}
                  className="p-1 text-gray-600 transition-colors hover:text-gray-900"
                >
                  <FaVolumeUp className="text-sm" />
                </button>
                {showVolumeSlider && (
                  <div className="absolute bottom-full right-0 mb-2 rounded-lg bg-white p-3 shadow-lg">
                    <input
                      type="range"
                      min="0"
                      max="1"
                      step="0.01"
                      value={volume}
                      onChange={(e) => setVolume(parseFloat(e.target.value))}
                      className="h-20 w-1 cursor-pointer appearance-none bg-gray-300 outline-none"
                      style={{ writingMode: 'bt-lr' }}
                    />
                  </div>
                )}
              </div>

              {/* Queue Button */}
              <button
                onClick={() => setShowQueue(!showQueue)}
                className={`p-1 transition-colors ${
                  showQueue ? 'text-primary' : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <FaList className="text-sm" />
              </button>

              {/* Close Button */}
              <button
                onClick={clearQueue}
                className="p-1 text-gray-600 transition-colors hover:text-red-600"
              >
                <FaTimes className="text-sm" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Volume Slider Backdrop */}
      {showVolumeSlider && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowVolumeSlider(false)}
        />
      )}
    </>
  );
};

export default GlobalMusicPlayer;
