'use client';
import React from 'react';
import { useMusicPlayer } from '@/context/MusicPlayerContext';
import { FaTimes, FaPlay, FaPause, FaTrash } from 'react-icons/fa';
import { IoReorderThree } from 'react-icons/io5';
import ImageComponent from '@/components/common/ImageComponent';

const QueueModal = () => {
  const {
    queue,
    currentIndex,
    currentTrack,
    isPlaying,
    showQueue,
    setShowQueue,
    playTrack,
    pauseTrack,
    resumeTrack,
    removeFromQueue,
    clearQueue,
  } = useMusicPlayer();

  if (!showQueue) return null;

  const formatTime = (time: number): string => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleTrackPlay = (track: any, index: number) => {
    if (currentTrack?._id === track._id) {
      if (isPlaying) {
        pauseTrack();
      } else {
        resumeTrack();
      }
    } else {
      playTrack(track, queue);
    }
  };

  const upcomingTracks = queue.slice(currentIndex + 1);
  const currentPlayingTrack = queue[currentIndex];

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 z-50 bg-black bg-opacity-50"
        onClick={() => setShowQueue(false)}
      />
      
      {/* Modal */}
      <div className="fixed right-4 top-4 bottom-20 z-50 w-96 rounded-lg bg-white shadow-xl">
        {/* Header */}
        <div className="flex items-center justify-between border-b border-gray-200 p-4">
          <h2 className="text-lg font-semibold text-gray-900">Next up</h2>
          <div className="flex items-center space-x-2">
            <button
              onClick={clearQueue}
              className="text-sm text-gray-500 hover:text-red-600"
            >
              Clear
            </button>
            <button
              onClick={() => setShowQueue(false)}
              className="p-1 text-gray-500 hover:text-gray-700"
            >
              <FaTimes className="text-sm" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex h-full flex-col overflow-hidden">
          {/* Currently Playing */}
          {currentPlayingTrack && (
            <div className="border-b border-gray-100 p-4">
              <h3 className="mb-3 text-sm font-medium text-gray-700">Now Playing</h3>
              <div className="flex items-center space-x-3">
                <div className="relative h-12 w-12 flex-shrink-0 overflow-hidden rounded-lg">
                  <ImageComponent
                    src={currentPlayingTrack.thumbnailUrl || '/assets/images/women.avif'}
                    alt={currentPlayingTrack.title}
                    className="h-full w-full object-cover"
                    fill
                  />
                  <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30">
                    <button
                      onClick={() => handleTrackPlay(currentPlayingTrack, currentIndex)}
                      className="flex h-6 w-6 items-center justify-center rounded-full bg-white text-black"
                    >
                      {isPlaying && currentTrack?._id === currentPlayingTrack._id ? (
                        <FaPause className="text-xs" />
                      ) : (
                        <FaPlay className="ml-0.5 text-xs" />
                      )}
                    </button>
                  </div>
                </div>
                <div className="min-w-0 flex-1">
                  <p className="truncate text-sm font-medium text-gray-900">
                    {currentPlayingTrack.title}
                  </p>
                  <p className="truncate text-xs text-gray-500">
                    {currentPlayingTrack.artist || currentPlayingTrack.creator.displayName}
                  </p>
                </div>
                <div className="text-xs text-gray-500">
                  {formatTime(currentPlayingTrack.duration)}
                </div>
              </div>
            </div>
          )}

          {/* Queue List */}
          <div className="flex-1 overflow-y-auto">
            {upcomingTracks.length > 0 ? (
              <div className="p-4">
                <h3 className="mb-3 text-sm font-medium text-gray-700">
                  Next ({upcomingTracks.length})
                </h3>
                <div className="space-y-2">
                  {upcomingTracks.map((track, index) => {
                    const actualIndex = currentIndex + 1 + index;
                    return (
                      <div
                        key={`${track._id}-${actualIndex}`}
                        className="group flex items-center space-x-3 rounded-lg p-2 hover:bg-gray-50"
                      >
                        {/* Drag Handle */}
                        <div className="cursor-move text-gray-400 opacity-0 group-hover:opacity-100">
                          <IoReorderThree className="text-lg" />
                        </div>

                        {/* Track Image */}
                        <div className="relative h-10 w-10 flex-shrink-0 overflow-hidden rounded">
                          <ImageComponent
                            src={track.thumbnailUrl || '/assets/images/women.avif'}
                            alt={track.title}
                            className="h-full w-full object-cover"
                            fill
                          />
                          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 opacity-0 transition-opacity group-hover:opacity-100">
                            <button
                              onClick={() => handleTrackPlay(track, actualIndex)}
                              className="flex h-5 w-5 items-center justify-center rounded-full bg-white text-black"
                            >
                              <FaPlay className="ml-0.5 text-xs" />
                            </button>
                          </div>
                        </div>

                        {/* Track Info */}
                        <div className="min-w-0 flex-1">
                          <p className="truncate text-sm font-medium text-gray-900">
                            {track.title}
                          </p>
                          <p className="truncate text-xs text-gray-500">
                            {track.artist || track.creator.displayName}
                          </p>
                        </div>

                        {/* Duration */}
                        <div className="text-xs text-gray-500">
                          {formatTime(track.duration)}
                        </div>

                        {/* Remove Button */}
                        <button
                          onClick={() => removeFromQueue(actualIndex)}
                          className="p-1 text-gray-400 opacity-0 transition-opacity hover:text-red-600 group-hover:opacity-100"
                        >
                          <FaTimes className="text-xs" />
                        </button>
                      </div>
                    );
                  })}
                </div>
              </div>
            ) : (
              <div className="flex h-full items-center justify-center p-8">
                <div className="text-center">
                  <div className="mb-2 text-4xl text-gray-300">🎵</div>
                  <p className="text-sm text-gray-500">No tracks in queue</p>
                  <p className="text-xs text-gray-400">
                    Add songs to see them here
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default QueueModal;
