import { encodeSqrtRatioX96 } from '@uniswap/v3-sdk';
import JSB<PERSON> from 'jsbi';
import { TickMath } from '@uniswap/v3-sdk';

import { UniswapV3FactoryAbi } from '@/lib/web3/abi/UniswapV3Factory';
import { erc20Abi } from 'viem';
import { parseUnits } from 'ethers';
import { ethers, formatUnits } from 'ethers';
import { Position, nearestUsableTick, Pool } from '@uniswap/v3-sdk';
import { Token } from '@uniswap/sdk-core';
import { toast } from 'react-toastify';
import axios from 'axios';
import { nftManagerAbi } from '@/lib/web3/abi/nftManagerAbi';
import { POOL_ABI } from '@/lib/web3/abi/POOL_ABI';

interface LiquidityProps {
  signer: ethers.JsonRpcSigner | undefined;
  tokenA: string;
  tokenB: string;
  amountA: string;
  amountB: string; // Changed from 'any' to 'string'
  recipient: string;
  chainId: number;
  // setTitle?: (msg: string) => void;
  // setDescription?: (msg: string) => void;
  updateStep: (title: string, description: string) => void;
  setCurrentStepIndex: any;
}
async function getPoolData(poolContract: any) {
  const [tickSpacing, fee, liquidity, slot0] = await Promise.all([
    poolContract.tickSpacing(),
    poolContract.fee(),
    poolContract.liquidity(),
    poolContract.slot0(),
  ]);

  return {
    tickSpacing,
    fee,
    liquidity,
    sqrtPriceX96: slot0[0],
    tick: slot0[1],
  };
}

export const mintLiquidity = async ({
  signer,
  tokenA,
  tokenB,
  amountA,
  amountB,
  recipient,
  // setTitle,
  // setDescription,

  updateStep,
  setCurrentStepIndex,
  chainId,
}: LiquidityProps): Promise<{
  loading: boolean;
  status: 'success' | 'error';
  error?: string;
  data?: any;
}> => {
  let loading = true;
  try {
    // const factoryAddress = '0x0227628f3F023bb0B980b67D528571c95c6DaC1c';
    const factoryAddress = '0x33128a8fC17869897dcE68Ed026d694621f6FDfD';
    // const positionManagerAddress = '0x1238536071E1c677A632429e3655c799b22cDA52';
    const positionManagerAddress = '0x03a520b32C04BF3bEEf7BEb72E919cf822Ed34f1';
    const fee = 3000;

    // Ensure proper token ordering (token0 < token1)
    const isTokenALower = tokenA.toLowerCase() < tokenB.toLowerCase();
    const token0Address = isTokenALower ? tokenA : tokenB;
    const token1Address = isTokenALower ? tokenB : tokenA;
    const amount0Input = isTokenALower ? amountA : amountB;
    const amount1Input = isTokenALower ? amountB : amountA;

    console.log('Token ordering:', {
      token0Address,
      token1Address,
      amount0Input,
      amount1Input,
    });

    // Get token decimals dynamically with validation
    const token0Contract = new ethers.Contract(token0Address, erc20Abi, signer);
    const token1Contract = new ethers.Contract(token1Address, erc20Abi, signer);

    let decimals0: number, decimals1: number;

    try {
      const [dec0, dec1] = await Promise.all([
        token0Contract.decimals(),
        token1Contract.decimals(),
      ]);

      // Convert to number and validate
      decimals0 = Number(dec0);
      decimals1 = Number(dec1);

      // Validate decimals are within acceptable range
      if (decimals0 <= 0 || decimals0 > 255 || isNaN(decimals0)) {
        throw new Error(`Invalid decimals for token0: ${decimals0}`);
      }
      if (decimals1 <= 0 || decimals1 > 255 || isNaN(decimals1)) {
        throw new Error(`Invalid decimals for token1: ${decimals1}`);
      }

      console.log('Token decimals:', { decimals0, decimals1 });
    } catch (error) {
      console.error('Error getting token decimals:', error);
      // Fallback to 18 decimals for standard tokens
      decimals0 = 18;
      decimals1 = 18;
      console.log('Using fallback decimals:', { decimals0, decimals1 });
    }

    // Create Token instances with validated decimals
    const token0 = new Token(chainId, token0Address, decimals0);
    const token1 = new Token(chainId, token1Address, decimals1);

    // Convert amounts to proper units with validation
    let amount0Parsed: bigint, amount1Parsed: bigint;

    try {
      amount0Parsed = parseUnits(amount0Input.toString(), decimals0);
      amount1Parsed = parseUnits(amount1Input.toString(), decimals1);

      // Validate amounts are not zero
      //@ts-ignore
      if (amount0Parsed <= 0n || amount1Parsed <= 0n) {
        throw new Error('Token amounts must be greater than zero');
      }

      console.log('Parsed amounts:', {
        amount0Parsed: amount0Parsed.toString(),
        amount1Parsed: amount1Parsed.toString(),
      });
    } catch (error) {
      console.error('Error parsing amounts:', error);
      throw new Error(`Invalid token amounts: ${error}`);
    }

    const amount0JSBI = JSBI.BigInt(amount0Parsed.toString());
    const amount1JSBI = JSBI.BigInt(amount1Parsed.toString());

    const factoryContract = new ethers.Contract(
      factoryAddress,
      UniswapV3FactoryAbi,
      signer,
    );

    const nonfungiblePositionManagerContract = new ethers.Contract(
      positionManagerAddress,
      nftManagerAbi,
      signer,
    );

    // Check balances
    const balance0 = await token0Contract.balanceOf(recipient);
    const balance1 = await token1Contract.balanceOf(recipient);

    console.log('Token Balances:', {
      token0: formatUnits(balance0, decimals0),
      token1: formatUnits(balance1, decimals1),
    });

    if (balance0 < amount0Parsed) {
      const error = `⚠️ Insufficient balance of token0. Required: ${formatUnits(amount0Parsed, decimals0)}, Available: ${formatUnits(balance0, decimals0)}`;
      console.log('⚠️', error);
      // toast.error('⚠️ ' + error);
      loading = false;
      return {
        loading,
        status: 'error',
        error,
      };
    }

    if (balance1 < amount1Parsed) {
      const error = `⚠️ Insufficient balance of token1. Required: ${formatUnits(amount1Parsed, decimals1)}, Available: ${formatUnits(balance1, decimals1)}`;
      console.log('⚠️', error);
      // toast.error('⚠️ ' + error);
      loading = false;
      return {
        loading,
        status: 'error',
        error,
      };
    }
    updateStep(
      'Checking or creating pool',
      'Looking up existing pool or deploying a new one...',
    );
    setCurrentStepIndex(0);
    // Get or create pool
    let deployedPoolAddress = await factoryContract.getPool(
      token0Address,
      token1Address,
      fee,
    );

    if (deployedPoolAddress === ethers.ZeroAddress) {
      console.log('🛠 Creating new pool...');
      try {
        const tx = await factoryContract.createPool(
          token0Address,
          token1Address,
          fee,
        );
        const receipt = await tx.wait();

        for (const log of receipt.logs) {
          try {
            const parsedLog = factoryContract.interface.parseLog(log);
            if (parsedLog && parsedLog.name === 'PoolCreated') {
              deployedPoolAddress = parsedLog.args.pool;
              console.log('✅ Pool Created at:', deployedPoolAddress);

              // Initialize the pool with calculated sqrt price
              const newPoolContract = new ethers.Contract(
                deployedPoolAddress,
                POOL_ABI,
                signer,
              );

              // Calculate initial price ratio
              const sqrtPriceX96 = encodeSqrtRatioX96(amount1JSBI, amount0JSBI);
              updateStep(
                'Initializing pool',
                'Setting initial price for the liquidity pool...',
              );
              setCurrentStepIndex(1);
              // setTitle?.('Initializing pool');
              // setDescription?.(
              //   'Setting initial price for the liquidity pool...',
              // );
              try {
                const initTx = await newPoolContract.initialize(
                  sqrtPriceX96.toString(),
                );
                await initTx.wait();
                console.log('✅ Pool initialized');
              } catch (error: any) {
                if (error.code === 4001) {
                  toast.error(
                    'Transaction rejected: Pool initialization cancelled by user',
                  );
                  throw new Error('Pool initialization rejected by user');
                }
                throw error;
              }

              break;
            }
          } catch (err) {
            console.log('Error parsing log:', err);
            continue;
          }
        }

        if (deployedPoolAddress === ethers.ZeroAddress) {
          throw new Error('❌ Failed to deploy or retrieve pool address.');
        }
      } catch (error: any) {
        if (error.code === 4001) {
          toast.error('Transaction rejected: Pool creation cancelled by user');
          throw new Error('Pool creation rejected by user');
        }
        throw error;
      }
    } else {
      console.log('✅ Pool already exists at:', deployedPoolAddress);
    }

    const poolContract = new ethers.Contract(
      deployedPoolAddress,
      POOL_ABI,
      signer,
    );

    // Get pool data
    const poolData = await getPoolData(poolContract);
    console.log('Pool data:', poolData);

    // Calculate tick range
    const currentTick = nearestUsableTick(
      Number(poolData.tick),
      Number(poolData.tickSpacing),
    );
    const tickSpacing = Number(poolData.tickSpacing);

    // Create a wider range for better liquidity provision
    const tickLower = Math.max(
      TickMath.MIN_TICK,
      currentTick - tickSpacing * 10, // Wider range
    );
    const tickUpper = Math.min(
      TickMath.MAX_TICK,
      currentTick + tickSpacing * 10, // Wider range
    );

    console.log('Tick range:', { currentTick, tickLower, tickUpper });

    // Create pool instance with validation
    let pool: Pool;
    try {
      pool = new Pool(
        token0,
        token1,
        Number(poolData.fee),
        poolData.sqrtPriceX96.toString(),
        poolData.liquidity.toString(),
        Number(poolData.tick),
      );
      console.log('Pool created successfully');
    } catch (error) {
      console.error('Error creating pool:', error);
      throw new Error(`Failed to create pool instance: ${error}`);
    }

    // Calculate liquidity from token amounts
    const position = Position.fromAmounts({
      pool,
      tickLower,
      tickUpper,
      amount0: amount0JSBI,
      amount1: amount1JSBI,
      useFullPrecision: false,
    });
    updateStep(
      'Approving tokens',
      'Sending approval transactions for token spend...',
    );
    setCurrentStepIndex(2);
    // setTitle?.('Approving tokens');
    // setDescription?.('Sending approval transactions for token spend...');

    console.log('Position created:', {
      liquidity: position.liquidity.toString(),
      amount0: position.amount0.toString(),
      amount1: position.amount1.toString(),
    });

    // Approve tokens with 10% buffer
    const approvalAmount0 = (amount0Parsed * BigInt(110)) / BigInt(100);
    const approvalAmount1 = (amount1Parsed * BigInt(110)) / BigInt(100);

    console.log('Approving tokens...');
    try {
      const approval0Tx = await token0Contract.approve(
        positionManagerAddress,
        approvalAmount0,
      );
      await approval0Tx.wait();
      console.log('✅ Token0 approved');
    } catch (error: any) {
      if (error.code === 4001) {
        toast.error('Transaction rejected: Token0 approval cancelled by user');
        throw new Error('Token0 approval rejected by user');
      }
      console.error('Token0 approval failed:', error);
      throw error;
    }

    try {
      const approval1Tx = await token1Contract.approve(
        positionManagerAddress,
        approvalAmount1,
      );
      await approval1Tx.wait();
      console.log('✅ Token1 approved');
    } catch (error: any) {
      if (error.code === 4001) {
        toast.error('Transaction rejected: Token1 approval cancelled by user');
        throw new Error('Token1 approval rejected by user');
      }
      console.error('Token1 approval failed:', error);
      throw error;
    }

    console.log('✅ All tokens approved');

    // Get mint amounts from position
    const { amount0: amount0Desired, amount1: amount1Desired } =
      position.mintAmounts;

    console.log('Mint amounts:', {
      amount0Desired: amount0Desired.toString(),
      amount1Desired: amount1Desired.toString(),
    });

    // Prepare mint parameters
    const mintParams = {
      token0: token0Address,
      token1: token1Address,
      fee: Number(poolData.fee),
      tickLower,
      tickUpper,
      amount0Desired: amount0Desired.toString(),
      amount1Desired: amount1Desired.toString(),
      amount0Min: JSBI.divide(amount0Desired, JSBI.BigInt(2)).toString(), // 50% slippage tolerance
      amount1Min: JSBI.divide(amount1Desired, JSBI.BigInt(2)).toString(), // 50% slippage tolerance
      recipient,
      deadline: Math.floor(Date.now() / 1000) + 20 * 60, // 20 minutes
    };

    updateStep(
      'Minting liquidity',
      'Sending transaction to mint your position...',
    );
    setCurrentStepIndex(3);
    // setTitle?.('Minting liquidity');
    // setDescription?.('Sending transaction to mint your position...');
    console.log('Mint parameters:', mintParams);

    // Mint the position
    console.log('Minting position...');
    let mintTx, mintReceipt;
    try {
      mintTx = await nonfungiblePositionManagerContract.mint(mintParams, {
        gasLimit: 1000000,
      });
      mintReceipt = await mintTx.wait();
      console.log('✅ Position minted successfully!', mintReceipt.hash);
    } catch (error: any) {
      if (error.code === 4001) {
        toast.error(
          'Transaction rejected: Liquidity minting cancelled by user',
        );
        throw new Error('Liquidity minting rejected by user');
      }
      console.error('Minting failed:', error);
      throw error;
    }

    // Prepare payload for API
    const payload = {
      transactionHash: mintReceipt.hash,
      userAddress: recipient,
      tokenAAddress: token0Address,
      tokenBAddress: token1Address,
      fee: Number(poolData.fee),
      tickLower,
      tickUpper,
      amountA: amount0Desired.toString(),
      amountB: amount1Desired.toString(), // Fixed: was using address1 instead of amount
      poolAddress: deployedPoolAddress,
      timestamp: new Date().toISOString(),
    };

    try {
      await axios.post(
        'https://temoc-app-server-htpjb.ondigitalocean.app/liquidity',
        payload,
      );
      console.log('✅ Data saved to API');
    } catch (apiError) {
      console.log('⚠️ API call failed:', apiError);
      // Don't fail the whole transaction if API fails
    }
    updateStep('Liquidity added', 'Your position was successfully minted!');
    setCurrentStepIndex(4);
    // setTitle?.('Liquidity added');
    // setDescription?.('Your position was successfully minted!');

    loading = false;
    return {
      loading,
      status: 'success',
      data: {
        txHash: mintReceipt?.hash,
        poolAddress: deployedPoolAddress,
        position,
        amounts: {
          amount0: amount0Desired.toString(),
          amount1: amount1Desired.toString(),
        },
      },
    };
  } catch (error) {
    console.log('Error in mintLiquidity:', error);
    loading = false;

    let errorMessage = 'Transaction failed';

    if (
      typeof error === 'object' &&
      error !== null &&
      'code' in error &&
      (error as any).code === 4001
    ) {
      errorMessage = 'Transaction rejected by user';
      toast.error('❌ Transaction cancelled by user');
    } else if (
      typeof error === 'object' &&
      error !== null &&
      'message' in error
    ) {
      const message = (error as { message: string }).message;

      // Handle specific rejection messages
      if (message.includes('rejected by user')) {
        errorMessage = message;
        toast.error('❌ ' + message);
      } else if (message.includes('User denied transaction signature')) {
        errorMessage = 'Transaction rejected by user';
        toast.error('❌ Transaction signature denied by user');
      } else if (message.includes('user rejected transaction')) {
        errorMessage = 'Transaction rejected by user';
        toast.error('❌ Transaction rejected in wallet');
      } else {
        errorMessage = message;
        toast.error('❌ ' + message);
      }
    } else {
      toast.error('❌ An unexpected error occurred');
    }

    return {
      loading,
      status: 'error',
      error: errorMessage,
    };
  }
};
